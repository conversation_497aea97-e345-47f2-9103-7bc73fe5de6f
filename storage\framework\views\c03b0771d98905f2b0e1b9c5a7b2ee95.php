<?php $__env->startSection('content'); ?>
<div class="container">
    <div class="row">
        <div class="col-md-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>My Subscriptions</h2>
                <a href="<?php echo e(route('pricing')); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Browse Plans
                </a>
            </div>

            <?php if(session('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo e(session('success')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if(session('info')): ?>
                <div class="alert alert-info alert-dismissible fade show" role="alert">
                    <?php echo e(session('info')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <!-- Current Subscription -->
            <?php if($activeSubscription): ?>
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-check-circle"></i> Current Subscription
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h4><?php echo e($activeSubscription->plan->name); ?></h4>
                                <p class="text-muted"><?php echo e($activeSubscription->plan->description); ?></p>
                                
                                <div class="row">
                                    <div class="col-sm-6">
                                        <strong>Amount:</strong> $<?php echo e(number_format($activeSubscription->amount, 2)); ?>

                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Billing:</strong> <?php echo e(ucfirst($activeSubscription->interval)); ?>

                                    </div>
                                </div>
                                
                                <div class="row mt-2">
                                    <div class="col-sm-6">
                                        <strong>Status:</strong> 
                                        <span class="badge bg-success"><?php echo e(ucfirst($activeSubscription->status)); ?></span>
                                    </div>
                                    <div class="col-sm-6">
                                        <strong>Next Billing:</strong> 
                                        <?php echo e($activeSubscription->current_period_end ? $activeSubscription->current_period_end->format('M d, Y') : 'N/A'); ?>

                                    </div>
                                </div>

                                <?php if($activeSubscription->auto_renew): ?>
                                    <div class="mt-3">
                                        <i class="fas fa-sync text-success"></i>
                                        <small class="text-success">Auto-renewal is enabled</small>
                                    </div>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 text-end">
                                <?php if($activeSubscription->isActive()): ?>
                                    <form method="POST" action="<?php echo e(route('user.subscriptions.cancel-subscription')); ?>" 
                                          onsubmit="return confirm('Are you sure you want to cancel your subscription? You will continue to have access until the end of your billing period.')">
                                        <?php echo csrf_field(); ?>
                                        <button type="submit" class="btn btn-outline-danger">
                                            <i class="fas fa-times"></i> Cancel Subscription
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="card mb-4">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-info-circle fa-3x text-muted mb-3"></i>
                        <h4>No Active Subscription</h4>
                        <p class="text-muted">You don't have any active subscription. Browse our plans to get started!</p>
                        <a href="<?php echo e(route('pricing')); ?>" class="btn btn-primary">
                            <i class="fas fa-eye"></i> View Plans
                        </a>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Subscription History -->
            <?php if($subscriptionHistory->count() > 0): ?>
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-history"></i> Subscription History
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Plan</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Period</th>
                                        <th>Created</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $subscriptionHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $subscription): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>
                                                <strong><?php echo e($subscription->plan->name); ?></strong>
                                                <br>
                                                <small class="text-muted"><?php echo e(ucfirst($subscription->interval)); ?></small>
                                            </td>
                                            <td>$<?php echo e(number_format($subscription->amount, 2)); ?></td>
                                            <td>
                                                <?php
                                                    $statusClass = match($subscription->status) {
                                                        'active' => 'bg-success',
                                                        'cancelled' => 'bg-warning',
                                                        'expired' => 'bg-danger',
                                                        default => 'bg-secondary'
                                                    };
                                                ?>
                                                <span class="badge <?php echo e($statusClass); ?>">
                                                    <?php echo e(ucfirst($subscription->status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php if($subscription->current_period_start && $subscription->current_period_end): ?>
                                                    <?php echo e($subscription->current_period_start->format('M d')); ?> - 
                                                    <?php echo e($subscription->current_period_end->format('M d, Y')); ?>

                                                <?php else: ?>
                                                    N/A
                                                <?php endif; ?>
                                            </td>
                                            <td><?php echo e($subscription->created_at->format('M d, Y')); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>

            <!-- Payment History -->
            <?php if($paymentHistory->count() > 0): ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-credit-card"></i> Recent Payments
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Plan</th>
                                        <th>Amount</th>
                                        <th>Status</th>
                                        <th>Payment Date</th>
                                        <th>Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $paymentHistory; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $payment): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td><?php echo e($payment->plan->name); ?></td>
                                            <td>$<?php echo e(number_format($payment->amount, 2)); ?></td>
                                            <td>
                                                <?php
                                                    $statusClass = match($payment->status) {
                                                        'succeeded' => 'bg-success',
                                                        'pending' => 'bg-warning',
                                                        'failed' => 'bg-danger',
                                                        'refunded' => 'bg-info',
                                                        default => 'bg-secondary'
                                                    };
                                                ?>
                                                <span class="badge <?php echo e($statusClass); ?>">
                                                    <?php echo e(ucfirst($payment->status)); ?>

                                                </span>
                                            </td>
                                            <td>
                                                <?php echo e($payment->paid_at ? $payment->paid_at->format('M d, Y g:i A') : 'N/A'); ?>

                                            </td>
                                            <td><?php echo e($payment->description ?? 'N/A'); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/user/subscriptions/index.blade.php ENDPATH**/ ?>