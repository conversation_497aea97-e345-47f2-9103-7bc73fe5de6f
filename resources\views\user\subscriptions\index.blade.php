@extends('layouts.app')

    <style>
        .subscription-card {
            transition: all 0.3s ease;
            border: none !important;
            border-radius: 20px !important;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1) !important;
        }

        .subscription-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15) !important;
        }

        .info-box {
            transition: all 0.3s ease;
        }

        .info-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            padding: 12px 24px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .btn-gradient:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            color: white;
        }

        .table-modern {
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
        }

        .table-modern thead th {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border: none;
            font-weight: 600;
            color: #1e3a8a;
            padding: 15px;
        }

        .table-modern tbody tr {
            border: none;
            transition: all 0.3s ease;
        }

        .table-modern tbody tr:hover {
            background-color: #f8fafc;
            transform: scale(1.01);
        }

        .table-modern tbody td {
            border: none;
            padding: 15px;
            vertical-align: middle;
        }

        .badge-modern {
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 12px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
    </style>

@section('content')
    <div class="container-fluid px-4">
        <div class="row">
            <div class="col-12">
                <!-- Header Section -->
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h2 class="mb-1" style="color: #1e3a8a; font-weight: 700;">
                            <i class="fas fa-crown" style="color: #f59e0b;"></i> My Subscriptions
                        </h2>
                        <p class="text-muted mb-0">Manage your subscription plans and payment history</p>
                    </div>
                    <a href="{{ route('pricing') }}" class="btn"
                        style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 10px; padding: 12px 24px; font-weight: 600; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3); transition: all 0.3s ease;">
                        <i class="fas fa-plus me-2"></i> Browse Plans
                    </a>
                </div>

                <!-- Alert Messages -->
                @if (session('success'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert"
                        style="border: none; border-radius: 15px; background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%); border-left: 4px solid #28a745;">
                        <i class="fas fa-check-circle me-2"></i>{{ session('success') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert"
                        style="border: none; border-radius: 15px; background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%); border-left: 4px solid #dc3545;">
                        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                @if (session('info'))
                    <div class="alert alert-info alert-dismissible fade show" role="alert"
                        style="border: none; border-radius: 15px; background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%); border-left: 4px solid #17a2b8;">
                        <i class="fas fa-info-circle me-2"></i>{{ session('info') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                @endif

                <!-- Current Subscription -->
                @if ($activeSubscription)
                    <div class="card mb-4"
                        style="border: none; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); overflow: hidden;">
                        <div class="card-header text-white"
                            style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); border: none; padding: 20px;">
                            <h5 class="mb-0" style="font-weight: 700;">
                                <i class="fas fa-check-circle me-2"></i> Current Subscription
                            </h5>
                        </div>
                        <div class="card-body" style="padding: 30px;">
                            <div class="row align-items-center">
                                <div class="col-md-8">
                                    <div class="d-flex align-items-center mb-3">
                                        <div class="subscription-icon me-3"
                                            style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 15px; display: flex; align-items: center; justify-content: center;">
                                            <i class="fas fa-crown" style="color: white; font-size: 24px;"></i>
                                        </div>
                                        <div>
                                            <h4 class="mb-1" style="color: #1e3a8a; font-weight: 700;">
                                                {{ $activeSubscription->plan->name }}</h4>
                                            <p class="text-muted mb-0">
                                                {{ $activeSubscription->plan->description ?? 'Premium subscription plan' }}
                                            </p>
                                        </div>
                                    </div>

                                    <div class="row g-4">
                                        <div class="col-sm-6">
                                            <div class="info-box"
                                                style="background: #f8fafc; padding: 15px; border-radius: 12px; border-left: 4px solid #3b82f6;">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-dollar-sign text-primary me-2"></i>
                                                    <div>
                                                        <small class="text-muted d-block">Amount</small>
                                                        <strong
                                                            style="color: #1e3a8a;">${{ number_format($activeSubscription->amount, 2) }}</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="info-box"
                                                style="background: #f8fafc; padding: 15px; border-radius: 12px; border-left: 4px solid #10b981;">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-calendar text-success me-2"></i>
                                                    <div>
                                                        <small class="text-muted d-block">Billing</small>
                                                        <strong
                                                            style="color: #1e3a8a;">{{ ucfirst($activeSubscription->interval ?? 'monthly') }}</strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row g-4 mt-2">
                                        <div class="col-sm-6">
                                            <div class="info-box"
                                                style="background: #f8fafc; padding: 15px; border-radius: 12px; border-left: 4px solid #10b981;">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-check-circle text-success me-2"></i>
                                                    <div>
                                                        <small class="text-muted d-block">Status</small>
                                                        <span class="badge"
                                                            style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); color: white; padding: 6px 12px; border-radius: 20px; font-weight: 600;">
                                                            {{ ucfirst($activeSubscription->status) }}
                                                        </span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-sm-6">
                                            <div class="info-box"
                                                style="background: #f8fafc; padding: 15px; border-radius: 12px; border-left: 4px solid #f59e0b;">
                                                <div class="d-flex align-items-center">
                                                    <i class="fas fa-clock text-warning me-2"></i>
                                                    <div>
                                                        <small class="text-muted d-block">Next Billing</small>
                                                        <strong style="color: #1e3a8a;">
                                                            {{ $activeSubscription->current_period_end ? $activeSubscription->current_period_end->format('M d, Y') : 'N/A' }}
                                                        </strong>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    @if ($activeSubscription->auto_renew ?? true)
                                        <div class="mt-4 p-3"
                                            style="background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%); border-radius: 12px; border: 1px solid #a7f3d0;">
                                            <div class="d-flex align-items-center">
                                                <i class="fas fa-sync text-success me-2"></i>
                                                <small class="text-success fw-semibold">Auto-renewal is enabled - Your
                                                    subscription will renew automatically</small>
                                            </div>
                                        </div>
                                    @endif
                                </div>
                                <div class="col-md-4 text-end">
                                    @if ($activeSubscription->isActive())
                                        <form method="POST" action="{{ route('user.subscriptions.cancel-subscription') }}"
                                            onsubmit="return confirm('Are you sure you want to cancel your subscription? You will continue to have access until the end of your billing period.')">
                                            @csrf
                                            <button type="submit" class="btn btn-outline-danger"
                                                style="border-radius: 12px; padding: 12px 24px; font-weight: 600; border: 2px solid #dc3545; transition: all 0.3s ease;">
                                                <i class="fas fa-times me-2"></i> Cancel Subscription
                                            </button>
                                        </form>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="card mb-4"
                        style="border: none; border-radius: 20px; box-shadow: 0 10px 30px rgba(0,0,0,0.1);">
                        <div class="card-body text-center py-5" style="padding: 50px 30px;">
                            <div class="empty-state-icon mb-4"
                                style="width: 100px; height: 100px; margin: 0 auto; background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%); border-radius: 50%; display: flex; align-items: center; justify-content: center;">
                                <i class="fas fa-crown" style="font-size: 40px; color: #9ca3af;"></i>
                            </div>
                            <h4 class="mb-3" style="color: #1e3a8a; font-weight: 700;">No Active Subscription</h4>
                            <p class="text-muted mb-4" style="font-size: 16px; max-width: 400px; margin: 0 auto;">You
                                don't have any active subscription. Browse our premium plans to unlock exclusive features
                                and get started!</p>
                            <a href="{{ route('pricing') }}" class="btn"
                                style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; border-radius: 12px; padding: 15px 30px; font-weight: 600; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3); transition: all 0.3s ease; text-decoration: none;">
                                <i class="fas fa-eye me-2"></i> View Plans
                            </a>
                        </div>
                    </div>
                @endif

                <!-- Subscription History -->
                @if ($subscriptionHistory->count() > 0)
                    <div class="card mb-4">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-history"></i> Subscription History
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Plan</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Period</th>
                                            <th>Created</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($subscriptionHistory as $subscription)
                                            <tr>
                                                <td>
                                                    <strong>{{ $subscription->plan->name }}</strong>
                                                    <br>
                                                    <small
                                                        class="text-muted">{{ ucfirst($subscription->interval) }}</small>
                                                </td>
                                                <td>${{ number_format($subscription->amount, 2) }}</td>
                                                <td>
                                                    @php
                                                        $statusClass = match ($subscription->status) {
                                                            'active' => 'bg-success',
                                                            'cancelled' => 'bg-warning',
                                                            'expired' => 'bg-danger',
                                                            default => 'bg-secondary',
                                                        };
                                                    @endphp
                                                    <span class="badge {{ $statusClass }}">
                                                        {{ ucfirst($subscription->status) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    @if ($subscription->current_period_start && $subscription->current_period_end)
                                                        {{ $subscription->current_period_start->format('M d') }} -
                                                        {{ $subscription->current_period_end->format('M d, Y') }}
                                                    @else
                                                        N/A
                                                    @endif
                                                </td>
                                                <td>{{ $subscription->created_at->format('M d, Y') }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Payment History -->
                @if ($paymentHistory->count() > 0)
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0">
                                <i class="fas fa-credit-card"></i> Recent Payments
                            </h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped">
                                    <thead>
                                        <tr>
                                            <th>Plan</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Payment Date</th>
                                            <th>Description</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach ($paymentHistory as $payment)
                                            <tr>
                                                <td>{{ $payment->plan->name }}</td>
                                                <td>${{ number_format($payment->amount, 2) }}</td>
                                                <td>
                                                    @php
                                                        $statusClass = match ($payment->status) {
                                                            'succeeded' => 'bg-success',
                                                            'pending' => 'bg-warning',
                                                            'failed' => 'bg-danger',
                                                            'refunded' => 'bg-info',
                                                            default => 'bg-secondary',
                                                        };
                                                    @endphp
                                                    <span class="badge {{ $statusClass }}">
                                                        {{ ucfirst($payment->status) }}
                                                    </span>
                                                </td>
                                                <td>
                                                    {{ $payment->paid_at ? $payment->paid_at->format('M d, Y g:i A') : 'N/A' }}
                                                </td>
                                                <td>{{ $payment->description ?? 'N/A' }}</td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        </div>
    </div>
@endsection
