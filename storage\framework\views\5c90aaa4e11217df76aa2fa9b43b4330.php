<style>
    .choose-plan-section {
        padding: 80px 0;
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        min-height: 100vh;
    }

    .plan-selection-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 0 20px;
    }

    .plan-selection-header {
        text-align: center;
        margin-bottom: 50px;
    }

    .plan-selection-header h1 {
        color: #1e3a8a;
        font-size: 2.5rem;
        margin-bottom: 15px;
        font-weight: 700;
    }

    .plan-selection-header p {
        color: #666;
        font-size: 1.1rem;
        max-width: 600px;
        margin: 0 auto;
    }

    .plan-checkout-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 40px;
        align-items: start;
    }

    .plan-showcase {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 20px;
        padding: 40px;
        border: 2px solid rgba(59, 130, 246, 0.1);
        box-shadow: 0 20px 40px rgba(59, 130, 246, 0.1);
        position: relative;
        overflow: hidden;
        height: fit-content;
    }

    .plan-showcase::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
    }

    .plan-showcase.popular::before {
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    }

    .popular-badge {
        position: absolute;
        top: 20px;
        right: -30px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        color: #fff;
        padding: 8px 40px;
        transform: rotate(45deg);
        font-size: 0.8rem;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    }

    .plan-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .plan-name {
        color: #1e3a8a;
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .plan-description {
        color: #666;
        font-size: 1rem;
        margin-bottom: 25px;
        line-height: 1.6;
    }

    .plan-price {
        display: flex;
        align-items: baseline;
        justify-content: center;
        gap: 5px;
        margin-bottom: 30px;
    }

    .price-currency {
        color: #1e3a8a;
        font-size: 1.5rem;
        font-weight: 600;
    }

    .price-amount {
        color: #1e3a8a;
        font-size: 3.5rem;
        font-weight: 800;
        line-height: 1;
    }

    .price-period {
        color: #666;
        font-size: 1.2rem;
        font-weight: 500;
    }

    .plan-features {
        margin-bottom: 30px;
    }

    .plan-features h4 {
        color: #1e3a8a;
        font-size: 1.2rem;
        font-weight: 600;
        margin-bottom: 20px;
        text-align: center;
    }

    .features-list {
        list-style: none;
        padding: 0;
    }

    .features-list li {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 0;
        color: #333;
        font-size: 1rem;
        border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    }

    .features-list li:last-child {
        border-bottom: none;
    }

    .feature-icon {
        width: 20px;
        height: 20px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 0.8rem;
        flex-shrink: 0;
    }

    .checkout-panel {
        background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
        border-radius: 20px;
        padding: 40px;
        border: 2px solid rgba(59, 130, 246, 0.1);
        box-shadow: 0 20px 40px rgba(59, 130, 246, 0.1);
        position: sticky;
        top: 100px;
    }

    .checkout-header {
        text-align: center;
        margin-bottom: 30px;
    }

    .checkout-header h3 {
        color: #1e3a8a;
        font-size: 1.5rem;
        font-weight: 700;
        margin-bottom: 10px;
    }

    .checkout-header p {
        color: #666;
        font-size: 0.95rem;
    }

    .order-summary {
        background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 30px;
        border: 1px solid rgba(59, 130, 246, 0.2);
    }

    .summary-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 0;
        color: #333;
    }

    .summary-item:not(:last-child) {
        border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    }

    .summary-label {
        font-weight: 500;
    }

    .summary-value {
        font-weight: 600;
        color: #1e3a8a;
    }

    .total-row {
        font-size: 1.1rem;
        font-weight: 700;
        color: #1e3a8a;
        margin-top: 10px;
        padding-top: 15px;
        border-top: 2px solid rgba(59, 130, 246, 0.2);
    }

    .payment-security {
        background: rgba(16, 185, 129, 0.1);
        border: 1px solid rgba(16, 185, 129, 0.2);
        border-radius: 12px;
        padding: 20px;
        margin-bottom: 30px;
        text-align: center;
    }

    .security-icon {
        width: 40px;
        height: 40px;
        background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #fff;
        font-size: 1.2rem;
        margin: 0 auto 15px;
    }

    .security-text {
        color: #059669;
        font-size: 0.9rem;
        font-weight: 600;
        margin-bottom: 5px;
    }

    .security-subtext {
        color: #666;
        font-size: 0.85rem;
        line-height: 1.4;
    }

    .checkout-actions {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .btn-checkout {
        background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
        color: #fff;
        border: none;
        padding: 18px 30px;
        border-radius: 12px;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
        cursor: pointer;
    }

    .btn-checkout:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1e3a8a 100%);
        transform: translateY(-3px);
        box-shadow: 0 12px 35px rgba(59, 130, 246, 0.6);
        color: #fff;
    }

    .btn-checkout:disabled {
        background: #9ca3af;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }

    .btn-back {
        background: transparent;
        color: #1e3a8a;
        border: 2px solid rgba(59, 130, 246, 0.3);
        padding: 15px 30px;
        border-radius: 12px;
        font-size: 1rem;
        font-weight: 600;
        text-decoration: none;
        text-align: center;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
    }

    .btn-back:hover {
        background: rgba(59, 130, 246, 0.1);
        border-color: rgba(59, 130, 246, 0.5);
        color: #1e3a8a;
        transform: translateY(-2px);
    }

    @media (max-width: 768px) {
        .choose-plan-section {
            padding: 40px 0;
        }

        .plan-checkout-grid {
            grid-template-columns: 1fr;
            gap: 30px;
        }

        .plan-showcase,
        .checkout-panel {
            padding: 30px 25px;
        }

        .plan-selection-header h1 {
            font-size: 2rem;
        }

        .price-amount {
            font-size: 2.5rem;
        }

        .checkout-panel {
            position: static;
        }
    }
</style>

<?php $__env->startSection('content'); ?>
    <div class="choose-plan-section">
        <div class="plan-selection-container">
            <div class="plan-selection-header">
                <h1>Complete Your Subscription</h1>
                <p>You're just one step away from accessing all the amazing features of <?php echo e($plan->name); ?>. Review your
                    selection and proceed to secure checkout.</p>
            </div>

            <?php if(session('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show mb-4" role="alert"
                    style="max-width: 600px; margin: 0 auto 40px;">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <?php echo e(session('error')); ?>

                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="plan-checkout-grid">
                <!-- Plan Showcase -->
                <div class="plan-showcase <?php echo e($plan->is_popular ? 'popular' : ''); ?>">
                    <?php if($plan->is_popular): ?>
                        <div class="popular-badge">Most Popular</div>
                    <?php endif; ?>

                    <div class="plan-header">
                        <h2 class="plan-name"><?php echo e($plan->name); ?></h2>
                        <?php if($plan->description): ?>
                            <p class="plan-description"><?php echo e($plan->description); ?></p>
                        <?php endif; ?>

                        <div class="plan-price">
                            <span class="price-currency">$</span>
                            <span class="price-amount"><?php echo e(number_format($plan->price, 0)); ?></span>
                            <span
                                class="price-period">/<?php echo e($plan->duration === 'yearly' ? 'year' : ($plan->duration === 'monthly' ? 'month' : 'week')); ?></span>
                        </div>
                    </div>

                    <?php if($plan->services && $plan->services->count() > 0): ?>
                        <div class="plan-features">
                            <h4>Everything included:</h4>
                            <ul class="features-list">
                                <?php $__currentLoopData = $plan->services; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $service): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li>
                                        <div class="feature-icon">
                                            <i class="fas fa-check"></i>
                                        </div>
                                        <span><?php echo e($service->name); ?></span>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Checkout Panel -->
                <div class="checkout-panel">
                    <div class="checkout-header">
                        <h3>Order Summary</h3>
                        <p>Review your subscription details below</p>
                    </div>

                    <div class="order-summary">
                        <div class="summary-item">
                            <span class="summary-label">Plan</span>
                            <span class="summary-value"><?php echo e($plan->name); ?></span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Billing Cycle</span>
                            <span class="summary-value"><?php echo e(ucfirst($plan->duration)); ?></span>
                        </div>
                        <div class="summary-item">
                            <span class="summary-label">Subtotal</span>
                            <span class="summary-value">$<?php echo e(number_format($plan->price, 2)); ?></span>
                        </div>
                        <div class="summary-item total-row">
                            <span class="summary-label">Total</span>
                            <span class="summary-value">$<?php echo e(number_format($plan->price, 2)); ?></span>
                        </div>
                    </div>

                    <div class="payment-security">
                        <div class="security-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <div class="security-text">Secure Payment</div>
                        <div class="security-subtext">
                            Your payment is processed securely through Stripe. Your subscription will auto-renew unless
                            cancelled.
                        </div>
                    </div>

                    <div class="checkout-actions">
                        <button type="button" id="checkout-button" class="btn-checkout">
                            <i class="fas fa-credit-card"></i>
                            Subscribe Now - $<?php echo e(number_format($plan->price, 2)); ?>

                        </button>
                        <a href="<?php echo e(route('pricing')); ?>" class="btn-back">
                            <i class="fas fa-arrow-left"></i>
                            Back to Plans
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Modal -->
    

    <style>
        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }
    </style>

<?php $__env->stopSection(); ?>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const checkoutButton = document.getElementById('checkout-button');
        const loadingModal = new bootstrap.Modal(document.getElementById('loadingModal'));
        const originalButtonText = checkoutButton.innerHTML;

        checkoutButton.addEventListener('click', function() {
            // Disable button and update styling immediately
            checkoutButton.disabled = true;
            checkoutButton.style.background = '#9ca3af';
            checkoutButton.style.boxShadow = 'none';
            checkoutButton.style.transform = 'none';
            checkoutButton.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';

            // Show loading modal after a small delay
            setTimeout(() => {
                loadingModal.show();
            }, 100);

            // Create checkout session
            fetch('<?php echo e(route('user.subscriptions.create-checkout-session')); ?>', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRF-TOKEN': '<?php echo e(csrf_token()); ?>'
                    },
                    body: JSON.stringify({
                        plan_id: '<?php echo e($plan->plans_id); ?>'
                    })
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        // Update modal content to show redirect message
                        const modalBody = document.querySelector('#loadingModal .modal-body');
                        modalBody.innerHTML = `
                            <div class="loading-icon mb-4">
                                <div style="width: 60px; height: 60px; margin: 0 auto; position: relative;">
                                    <div style="width: 60px; height: 60px; border: 4px solid #dcfce7; border-top: 4px solid #10b981; border-radius: 50%; animation: spin 1s linear infinite;"></div>
                                    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); color: #10b981; font-size: 1.2rem;">
                                        <i class="fas fa-check"></i>
                                    </div>
                                </div>
                            </div>
                            <h4 style="color: #1e3a8a; font-weight: 700; margin-bottom: 15px;">Redirecting to Stripe...</h4>
                            <p style="color: #666; margin-bottom: 0; font-size: 1rem;">Please wait while we redirect you to secure checkout...</p>
                            <div style="margin-top: 20px; padding: 15px; background: rgba(16, 185, 129, 0.1); border-radius: 10px; border: 1px solid rgba(16, 185, 129, 0.2);">
                                <i class="fas fa-shield-alt" style="color: #10b981; margin-right: 8px;"></i>
                                <small style="color: #059669; font-weight: 600;">Secured by Stripe</small>
                            </div>
                        `;

                        // Redirect to Stripe checkout after a short delay
                        setTimeout(() => {
                            window.location.href = data.checkout_url;
                        }, 1000);
                    } else {
                        // Hide loading modal
                        loadingModal.hide();

                        // Re-enable button and restore styling
                        checkoutButton.disabled = false;
                        checkoutButton.style.background = '';
                        checkoutButton.style.boxShadow = '';
                        checkoutButton.style.transform = '';
                        checkoutButton.innerHTML = originalButtonText;

                        // Show error with SweetAlert if available, otherwise use alert
                        if (typeof Swal !== 'undefined') {
                            Swal.fire({
                                title: 'Error!',
                                text: data.message ||
                                    'An error occurred. Please try again.',
                                icon: 'error',
                                confirmButtonColor: '#3b82f6',
                                confirmButtonText: 'Try Again'
                            });
                        } else {
                            alert(data.message || 'An error occurred. Please try again.');
                        }
                    }
                })
                .catch(error => {
                    console.error('Error:', error);

                    // Hide loading modal
                    loadingModal.hide();

                    // Re-enable button and restore styling
                    checkoutButton.disabled = false;
                    checkoutButton.style.background = '';
                    checkoutButton.style.boxShadow = '';
                    checkoutButton.style.transform = '';
                    checkoutButton.innerHTML = originalButtonText;

                    // Show error with SweetAlert if available, otherwise use alert
                    if (typeof Swal !== 'undefined') {
                        Swal.fire({
                            title: 'Error!',
                            text: 'An error occurred. Please try again.',
                            icon: 'error',
                            confirmButtonColor: '#3b82f6',
                            confirmButtonText: 'Try Again'
                        });
                    } else {
                        alert('An error occurred. Please try again.');
                    }
                });
        });
    });
</script>
    
<?php echo $__env->make('layouts.app', array_diff_key(get_defined_vars(), ['__data' => 1, '__path' => 1]))->render(); ?><?php /**PATH C:\xampp\htdocs\trash\resources\views/user/subscriptions/choose-plan.blade.php ENDPATH**/ ?>